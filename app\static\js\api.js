// API utility functions for DreamBig Real Estate Platform

class ApiClient {
    constructor() {
        this.baseUrl = API_CONFIG.BASE_URL + API_CONFIG.API_VERSION;
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }
    
    // Get authorization headers
    getAuthHeaders() {
        const token = authManager.getIdToken();
        if (token) {
            return {
                ...this.defaultHeaders,
                'Authorization': `Bearer ${token}`
            };
        }
        return this.defaultHeaders;
    }
    
    // Generic API request method
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: this.getAuthHeaders(),
            ...options
        };
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (response.ok) {
                return { success: true, data, status: response.status };
            } else {
                // Handle token expiration
                if (response.status === 401) {
                    try {
                        await authManager.refreshToken();
                        // Retry request with new token
                        config.headers = this.getAuthHeaders();
                        const retryResponse = await fetch(url, config);
                        const retryData = await retryResponse.json();
                        
                        if (retryResponse.ok) {
                            return { success: true, data: retryData, status: retryResponse.status };
                        }
                    } catch (refreshError) {
                        console.error('Token refresh failed:', refreshError);
                        authManager.logout();
                    }
                }
                
                return {
                    success: false,
                    error: data.detail || data.message || 'Request failed',
                    status: response.status
                };
            }
        } catch (error) {
            console.error('API request error:', error);
            return {
                success: false,
                error: 'Network error. Please check your connection.',
                status: 0
            };
        }
    }
    
    // GET request
    async get(endpoint, params = {}) {
        const queryString = UTILS.getQueryString(params);
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        
        return this.request(url, {
            method: 'GET'
        });
    }
    
    // POST request
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    
    // PUT request
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }
    
    // DELETE request
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }
    
    // Upload file
    async uploadFile(endpoint, file, additionalData = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        Object.keys(additionalData).forEach(key => {
            formData.append(key, additionalData[key]);
        });
        
        const token = authManager.getIdToken();
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        
        return this.request(endpoint, {
            method: 'POST',
            headers,
            body: formData
        });
    }
    
    // Properties API
    async getProperties(params = {}) {
        return this.get(API_CONFIG.ENDPOINTS.PROPERTIES, params);
    }
    
    async getProperty(id) {
        return this.get(API_CONFIG.ENDPOINTS.PROPERTY_DETAIL.replace('{id}', id));
    }
    
    async createProperty(propertyData) {
        return this.post(API_CONFIG.ENDPOINTS.PROPERTIES, propertyData);
    }
    
    async updateProperty(id, propertyData) {
        return this.put(API_CONFIG.ENDPOINTS.PROPERTY_DETAIL.replace('{id}', id), propertyData);
    }
    
    async deleteProperty(id) {
        return this.delete(API_CONFIG.ENDPOINTS.PROPERTY_DETAIL.replace('{id}', id));
    }
    
    async uploadPropertyImage(propertyId, file) {
        const endpoint = API_CONFIG.ENDPOINTS.PROPERTY_UPLOAD_IMAGE.replace('{id}', propertyId);
        return this.uploadFile(endpoint, file);
    }
    
    // Search API
    async searchProperties(query, filters = {}) {
        const params = { query, ...filters };
        return this.get(API_CONFIG.ENDPOINTS.SEARCH, params);
    }
    
    // User API
    async getCurrentUser() {
        return this.get(API_CONFIG.ENDPOINTS.USER_ME);
    }
    
    async updateUser(userData) {
        return this.put(API_CONFIG.ENDPOINTS.USER_ME, userData);
    }
    
    async getUserPreferences() {
        return this.get(API_CONFIG.ENDPOINTS.USER_PREFERENCES);
    }
    
    async updateUserPreferences(preferences) {
        return this.put(API_CONFIG.ENDPOINTS.USER_PREFERENCES, preferences);
    }
    
    async getFavorites() {
        return this.get(API_CONFIG.ENDPOINTS.USER_FAVORITES);
    }
    
    async addToFavorites(propertyId) {
        return this.post(`${API_CONFIG.ENDPOINTS.USER_FAVORITES}/${propertyId}`);
    }
    
    async removeFromFavorites(propertyId) {
        return this.delete(`${API_CONFIG.ENDPOINTS.USER_FAVORITES}/${propertyId}`);
    }
    
    async getRecentlyViewed() {
        return this.get(API_CONFIG.ENDPOINTS.USER_RECENTLY_VIEWED);
    }
    
    async getRecommendations() {
        return this.get(API_CONFIG.ENDPOINTS.USER_RECOMMENDATIONS);
    }
    
    async getUserAnalytics() {
        return this.get(API_CONFIG.ENDPOINTS.USER_ANALYTICS);
    }
    
    // Investments API
    async getInvestments() {
        return this.get(API_CONFIG.ENDPOINTS.INVESTMENTS);
    }
    
    async getInvestment(id) {
        return this.get(API_CONFIG.ENDPOINTS.INVESTMENT_DETAIL.replace('{id}', id));
    }
    
    async createInvestment(investmentData) {
        return this.post(API_CONFIG.ENDPOINTS.INVESTMENTS, investmentData);
    }

    async updateInvestment(id, investmentData) {
        return this.put(API_CONFIG.ENDPOINTS.INVESTMENT_DETAIL.replace('{id}', id), investmentData);
    }

    async deleteInvestment(id) {
        return this.delete(API_CONFIG.ENDPOINTS.INVESTMENT_DETAIL.replace('{id}', id));
    }
    
    // Services API
    async getServices() {
        return this.get(API_CONFIG.ENDPOINTS.SERVICES);
    }
    
    async getServiceProviders(category = '') {
        const params = category ? { service_type: category } : {};
        return this.get(API_CONFIG.ENDPOINTS.SERVICE_PROVIDERS, params);
    }
    
    async createServiceBooking(bookingData) {
        return this.post(API_CONFIG.ENDPOINTS.SERVICE_BOOKINGS, bookingData);
    }
    
    async getServiceBookings() {
        return this.get(API_CONFIG.ENDPOINTS.SERVICE_BOOKINGS);
    }
}

// Create global API client instance
const apiClient = new ApiClient();

// Utility functions for common API operations
const ApiUtils = {
    // Handle API response with loading and error handling
    async handleApiCall(apiCall, options = {}) {
        const {
            showLoading: shouldShowLoading = true,
            successMessage = null,
            errorMessage = null,
            onSuccess = null,
            onError = null
        } = options;
        
        if (shouldShowLoading) {
            showLoading(true);
        }
        
        try {
            const response = await apiCall();
            
            if (response.success) {
                if (successMessage) {
                    showToast(successMessage, 'success');
                }
                
                if (onSuccess) {
                    onSuccess(response.data);
                }
                
                return response.data;
            } else {
                const message = errorMessage || response.error || 'Operation failed';
                showToast(message, 'error');
                
                if (onError) {
                    onError(response.error);
                }
                
                return null;
            }
        } catch (error) {
            console.error('API call error:', error);
            const message = errorMessage || 'An unexpected error occurred';
            showToast(message, 'error');
            
            if (onError) {
                onError(error.message);
            }
            
            return null;
        } finally {
            if (shouldShowLoading) {
                showLoading(false);
            }
        }
    },
    
    // Paginated data fetcher
    async fetchPaginatedData(fetchFunction, params = {}) {
        const defaultParams = {
            skip: 0,
            limit: APP_CONFIG.PAGINATION.DEFAULT_LIMIT,
            ...params
        };
        
        return fetchFunction(defaultParams);
    },
    
    // Batch operations
    async batchOperation(operations, options = {}) {
        const {
            concurrency = 3,
            showProgress = false
        } = options;
        
        const results = [];
        const errors = [];
        
        for (let i = 0; i < operations.length; i += concurrency) {
            const batch = operations.slice(i, i + concurrency);
            
            if (showProgress) {
                showToast(`Processing ${i + 1}-${Math.min(i + concurrency, operations.length)} of ${operations.length}`, 'info');
            }
            
            const batchPromises = batch.map(async (operation, index) => {
                try {
                    const result = await operation();
                    return { success: true, data: result, index: i + index };
                } catch (error) {
                    return { success: false, error, index: i + index };
                }
            });
            
            const batchResults = await Promise.all(batchPromises);
            
            batchResults.forEach(result => {
                if (result.success) {
                    results.push(result);
                } else {
                    errors.push(result);
                }
            });
        }
        
        return { results, errors };
    }
};

// Export API client and utilities
window.apiClient = apiClient;
window.ApiUtils = ApiUtils;

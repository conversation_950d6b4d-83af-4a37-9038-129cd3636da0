{% extends "base.html" %}

{% block title %}Services - DreamBig Real Estate Platform{% endblock %}

{% block content %}
    <!-- Services Section -->
    <section class="services-section" style="margin-top: 80px; padding-top: 2rem;">
        <div class="container">
            <div class="section-header">
                <h2>Professional Services</h2>
                <div class="service-actions">
                    <button class="btn btn-primary" onclick="showBookServiceModal()">
                        <i class="fas fa-calendar-plus"></i> Book Service
                    </button>
                </div>
            </div>
            
            <!-- Service Categories -->
            <div class="service-categories" style="display: flex; gap: 1rem; margin-bottom: 2rem; flex-wrap: wrap;">
                <button class="category-btn active" data-category="" onclick="filterServices('')">
                    All Services
                </button>
                <button class="category-btn" data-category="legal" onclick="filterServices('legal')">
                    <i class="fas fa-gavel"></i> Legal
                </button>
                <button class="category-btn" data-category="financial" onclick="filterServices('financial')">
                    <i class="fas fa-calculator"></i> Financial
                </button>
                <button class="category-btn" data-category="maintenance" onclick="filterServices('maintenance')">
                    <i class="fas fa-tools"></i> Maintenance
                </button>
                <button class="category-btn" data-category="interior" onclick="filterServices('interior')">
                    <i class="fas fa-paint-brush"></i> Interior
                </button>
                <button class="category-btn" data-category="moving" onclick="filterServices('moving')">
                    <i class="fas fa-truck"></i> Moving
                </button>
            </div>
            
            <!-- Services Grid -->
            <div class="services-grid" id="services-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
                <!-- Services will be loaded here -->
            </div>
            
            <!-- My Bookings Section -->
            <div class="my-bookings" style="margin-top: 3rem;">
                <h3>My Service Bookings</h3>
                <div class="bookings-grid" id="bookings-grid" style="display: grid; gap: 1rem; margin-top: 1rem;">
                    <!-- Bookings will be loaded here -->
                </div>
                
                <div id="bookings-empty" class="empty-state" style="text-align: center; padding: 2rem; color: var(--text-secondary); display: none;">
                    <i class="fas fa-calendar" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p>No service bookings yet</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Authentication Required Message -->
    <div id="auth-required" class="auth-required" style="display: none; text-align: center; padding: 4rem 2rem; margin-top: 80px;">
        <div class="container">
            <i class="fas fa-lock" style="font-size: 3rem; color: var(--text-secondary); margin-bottom: 1rem;"></i>
            <h2>Authentication Required</h2>
            <p>Please login to view and book services</p>
            <div style="margin-top: 2rem;">
                <button class="btn btn-primary" onclick="showLoginModal()">Login</button>
                <button class="btn btn-outline" onclick="showRegisterModal()">Sign Up</button>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<style>
    .category-btn {
        padding: 0.75rem 1.5rem;
        border: 1px solid var(--border-color);
        background: var(--surface-color);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .category-btn:hover,
    .category-btn.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }
    
    .service-card {
        background: var(--surface-color);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-sm);
        transition: var(--transition);
    }
    
    .service-card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }
    
    .booking-card {
        background: var(--surface-color);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
</style>

<script>
    let currentCategory = '';
    
    // Initialize services page
    document.addEventListener('DOMContentLoaded', function() {
        // Check if user is authenticated for bookings
        setTimeout(function() {
            if (authManager && authManager.isAuthenticated()) {
                loadServices();
                loadBookings();
            } else {
                loadServices(); // Services can be viewed without auth
                document.querySelector('.my-bookings').style.display = 'none';
            }
        }, 500);
    });
    
    // Listen for auth state changes
    document.addEventListener('authStateChanged', function(e) {
        if (e.detail.isAuthenticated) {
            document.querySelector('.my-bookings').style.display = 'block';
            loadBookings();
        } else {
            document.querySelector('.my-bookings').style.display = 'none';
        }
    });
    
    // Load services
    async function loadServices() {
        try {
            // Try to load from API first
            const response = await apiClient.getServiceProviders(currentCategory);
            if (response && response.length > 0) {
                // Convert API response to expected format
                const services = response.map(provider => ({
                    id: provider.id,
                    name: provider.name,
                    category: provider.service_type,
                    description: provider.description || "Professional service provider",
                    price: 5000, // Default price - should be in provider data
                    rating: 4.5, // Default rating - should be calculated
                    provider: provider.name,
                    image: "/static/images/service-default.jpg"
                }));
                renderServices(services);
                return;
            }
        } catch (error) {
            console.error('Error loading services from API:', error);
        }

        // Fallback to mock services data if API fails or returns empty
        const mockServices = [
            {
                id: 1,
                name: "Property Legal Consultation",
                category: "legal",
                description: "Expert legal advice for property transactions",
                price: 5000,
                rating: 4.8,
                provider: "Legal Associates",
                image: "/static/images/legal-service.jpg"
            },
            {
                id: 2,
                name: "Home Loan Assistance",
                category: "financial",
                description: "Get the best home loan deals with expert guidance",
                price: 3000,
                rating: 4.9,
                provider: "Finance Pro",
                image: "/static/images/finance-service.jpg"
            },
            {
                id: 3,
                name: "Property Maintenance",
                category: "maintenance",
                description: "Complete property maintenance and repair services",
                price: 2000,
                rating: 4.7,
                provider: "Fix It All",
                image: "/static/images/maintenance-service.jpg"
            },
            {
                id: 4,
                name: "Interior Design",
                category: "interior",
                description: "Transform your space with professional interior design",
                price: 15000,
                rating: 4.9,
                provider: "Design Studio",
                image: "/static/images/interior-service.jpg"
            },
            {
                id: 5,
                name: "Packers & Movers",
                category: "moving",
                description: "Safe and reliable moving services",
                price: 8000,
                rating: 4.6,
                provider: "Move Easy",
                image: "/static/images/moving-service.jpg"
            }
        ];

        renderServices(mockServices);
    }
    
    // Load bookings
    async function loadBookings() {
        if (!authManager.isAuthenticated()) return;

        try {
            const bookings = await apiClient.getServiceBookings();
            renderBookings(bookings || []);
        } catch (error) {
            console.error('Error loading bookings:', error);
            // Show empty state on error
            renderBookings([]);
        }
    }
    
    // Filter services by category
    function filterServices(category) {
        currentCategory = category;
        
        // Update active button
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.category === category);
        });
        
        loadServices(); // Reload with filter
    }
    
    // Render services
    function renderServices(services) {
        const grid = document.getElementById('services-grid');
        
        // Filter by category if selected
        const filteredServices = currentCategory 
            ? services.filter(service => service.category === currentCategory)
            : services;
        
        if (filteredServices.length === 0) {
            grid.innerHTML = `
                <div class="empty-state" style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: var(--text-secondary);">
                    <i class="fas fa-tools" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                    <h3>No services found</h3>
                    <p>Try selecting a different category</p>
                </div>
            `;
            return;
        }
        
        grid.innerHTML = filteredServices.map(service => createServiceCard(service)).join('');
    }
    
    // Create service card
    function createServiceCard(service) {
        return `
            <div class="service-card">
                <div class="service-image" style="width: 100%; height: 150px; background: var(--border-color); border-radius: var(--border-radius); margin-bottom: 1rem; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-image" style="font-size: 2rem; color: var(--text-secondary);"></i>
                </div>
                
                <h3 style="margin-bottom: 0.5rem; color: var(--primary-color);">${service.name}</h3>
                
                <div class="service-meta" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; font-size: 0.875rem; color: var(--text-secondary);">
                    <span><i class="fas fa-user"></i> ${service.provider}</span>
                    <span><i class="fas fa-star" style="color: var(--accent-color);"></i> ${service.rating}</span>
                </div>
                
                <p style="color: var(--text-secondary); margin-bottom: 1rem; line-height: 1.5;">${service.description}</p>
                
                <div class="service-footer" style="display: flex; justify-content: space-between; align-items: center;">
                    <div class="service-price" style="font-size: 1.25rem; font-weight: 700; color: var(--primary-color);">
                        ${UTILS.formatCurrency(service.price)}
                    </div>
                    
                    <button class="btn btn-primary" onclick="bookService(${service.id})">
                        Book Now
                    </button>
                </div>
            </div>
        `;
    }
    
    // Render bookings
    function renderBookings(bookings) {
        const grid = document.getElementById('bookings-grid');
        const emptyState = document.getElementById('bookings-empty');
        
        if (bookings.length === 0) {
            grid.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }
        
        grid.style.display = 'grid';
        emptyState.style.display = 'none';
        
        grid.innerHTML = bookings.map(booking => createBookingCard(booking)).join('');
    }
    
    // Create booking card
    function createBookingCard(booking) {
        return `
            <div class="booking-card">
                <div class="booking-info">
                    <h4>${booking.service_name}</h4>
                    <p style="color: var(--text-secondary); font-size: 0.875rem;">
                        ${booking.provider} • ${UTILS.formatDate(booking.booking_date)}
                    </p>
                </div>
                
                <div class="booking-status">
                    <span class="status-badge" style="padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.75rem; background: var(--primary-color); color: white;">
                        ${booking.status}
                    </span>
                </div>
            </div>
        `;
    }
    
    // Service actions
    function bookService(serviceId) {
        if (!authManager.isAuthenticated()) {
            showToast('Please login to book services', 'warning');
            showLoginModal();
            return;
        }
        
        showToast('Service booking feature coming soon!', 'info');
    }
    
    function showBookServiceModal() {
        if (!authManager.isAuthenticated()) {
            showToast('Please login to book services', 'warning');
            showLoginModal();
            return;
        }
        
        showToast('Service booking modal coming soon!', 'info');
    }
</script>
{% endblock %}
